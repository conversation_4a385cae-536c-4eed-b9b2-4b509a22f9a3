import Link from "next/link";
import styled from "styled-components";

import { SvgIconMessageWhite } from "@/components/ui/Icon/IconMessageWhite";

const Wrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--global-header-height);
  width: 55px;
  border-left: 1px solid #034888;
  border-right: 1px solid #034888;
`;

const StyledLink = styled(Link)`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 40px;
  transition: 0.3s;
  cursor: pointer;
  border-radius: 4px;

  &:hover,
  &:focus-visible {
    background-color: rgba(0, 0, 0, 0.2);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  }

  &:focus-visible {
    transition: none;
  }
`;

const urlInformation = process.env
  .NEXT_PUBLIC_URL_HEALTHTECH_INFORMATION as string;

export const HeaderMessage: React.FC = () => {
  return (
    <Wrapper>
      <StyledLink
        href={urlInformation}
        target="_blank"
        rel="noopener noreferrer"
      >
        <SvgIconMessageWhite />
      </StyledLink>
    </Wrapper>
  );
};
