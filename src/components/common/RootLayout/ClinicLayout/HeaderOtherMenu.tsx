import React from "react";

import { Dropdown } from "antd";
import styled from "styled-components";

import { SvgIconOtherMenu } from "@/components/ui/Icon/IconOtherMenu";
import { SvgIconOnlineConsultation } from "@/components/ui/Icon/IconOnlineConsultation";
import { SvgIconWebPatientForm } from "@/components/ui/Icon/IconWebPatientForm";
import { SvgIconAi } from "@/components/ui/Icon/IconAi";
import { SvgIconTask } from "@/components/ui/Icon/IconTask";
import { SvgIconTestCollaborationWhite } from "@/components/ui/Icon/IconTestCollaborationWhite";
import { SvgIconReceiptWhite } from "@/components/ui/Icon/IconReceiptWhite";
import { SvgIconPrintoutWhite } from "@/components/ui/Icon/IconPrintoutWhite";

const Wrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--global-header-height);
  width: 55px;
  border-left: 1px solid #034888;
`;

const StyledIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 40px;
  transition: 0.3s;
  cursor: pointer;
  border-radius: 4px;

  &:hover,
  &:focus-visible {
    background-color: rgba(0, 0, 0, 0.2);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  }

  &:focus-visible {
    transition: none;
  }
`;

const MenuGrid = styled.div`
  margin-top: 4px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  padding: 16px;
  width: 372px;
  border-radius: 24px;
  box-shadow:
    0 4px 8px 3px rgba(0, 0, 0, 0.15),
    0 1px 8px 0 rgba(0, 0, 0, 0.3);
  border: solid 8px #e2e3e5;
  background-color: #fff;
`;

const MenuItem = styled.div`
  width: 100px;
  height: 56px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 4px;
  border: solid 1px #e2e3e5;
  cursor: pointer;
  background-color: #fbfcfe;
`;

const MenuIcon = styled.div`
  width: 24px;
  height: 24px;
  color: #005bac;
`;

const MenuLabel = styled.span`
  width: 84px;
  font-size: 12px;
  font-family: NotoSansJP;
  color: #243544;
  text-align: center;
  line-height: 1;
`;

// Menu items data
const menuItems = [
  {
    key: "online-medical",
    label: "オンライン診療",
    icon: <SvgIconOnlineConsultation />,
  },
  {
    key: "web-questionnaire",
    label: "Web問診",
    icon: <SvgIconWebPatientForm />,
  },
  { key: "ai-assist", label: "AIアシスト", icon: <SvgIconAi /> },
  { key: "tasks", label: "タスク", icon: <SvgIconTask /> },
  {
    key: "lab-integration",
    label: "検査連携",
    icon: <SvgIconTestCollaborationWhite />,
  },
  { key: "receipt", label: "レセプト", icon: <SvgIconReceiptWhite /> },
  { key: "report-printing", label: "帳票印刷", icon: <SvgIconPrintoutWhite /> },
];

const handleMenuClick = (_key: string) => {
  // TODO: Implement navigation logic based on key
  // Example: router.push(`/${key}`) or specific routing logic
};

const dropdownRender = () => (
  <MenuGrid>
    {menuItems.map((item) => (
      <MenuItem key={item.key} onClick={() => handleMenuClick(item.key)}>
        <MenuIcon>{item.icon}</MenuIcon>
        <MenuLabel>{item.label}</MenuLabel>
      </MenuItem>
    ))}
  </MenuGrid>
);

export const HeaderOtherMenu: React.FC = () => {
  return (
    <Wrapper>
      <Dropdown
        trigger={["click"]}
        dropdownRender={dropdownRender}
        placement="bottom"
        arrow={false}
        align={{ offset: [0, 8] }}
      >
        <StyledIcon>
          <SvgIconOtherMenu />
        </StyledIcon>
      </Dropdown>
    </Wrapper>
  );
};
