import React from "react";

import styled from "styled-components";
import dayjs from "dayjs";

const ReserveSummarySectionContainer = styled.div`
  height: 160px;
  border-radius: 12px;
  background-color: #fff;
  display: flex;
  overflow: hidden;
`;

const DateSection = styled.div`
  background-color: #005bac;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 26px 42px;
  min-width: 200px;
`;

const YearText = styled.div`
  font-size: 22px;

  .number {
    font-family: Roboto;
  }
`;

const DateText = styled.div`
  line-height: 1.11;
  font-family: NotoSansJP;
  font-size: 32px;

  .number {
    font-family: Roboto;
    font-size: 54px;
  }
`;

const DayText = styled.div`
  font-family: NotoSansJP;
  font-size: 20px;
`;

const StatsSection = styled.div`
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
`;

const StatsGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;

  &:nth-child(2) {
    border-top: 1px solid #e2e3e5;
    padding-top: 12px;
  }
`;

const StatsTitle = styled.div`
  font-size: 16px;
  font-weight: bold;
  color: #243544;
  line-height: 1;
`;

const StatsRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
`;

const StatsLabel = styled.div`
  font-size: 16px;
  color: #243544;
  padding-left: 20px;
  line-height: 1;
`;

const StatsValue = styled.div`
  color: #007aff;
  line-height: 1;
  .number {
    font-family: Roboto;
  }
`;

const formatText = (dateText: string) => {
  return dateText.split("").map((char, index) => {
    const isNumber = /\d/.test(char);
    const className = isNumber ? "number" : "character";
    return (
      <span key={index} className={className}>
        {char}
      </span>
    );
  });
};

export const ReserveSummarySection = () => {
  const currentDate = dayjs();
  const year = currentDate.format("YYYY年");
  const monthDay = currentDate.format("M月D日");
  const dayOfWeek = currentDate.format("dddd");

  return (
    <ReserveSummarySectionContainer>
      <DateSection>
        <YearText>{formatText(year)}</YearText>
        <DateText>{formatText(monthDay)}</DateText>
        <DayText>{dayOfWeek}</DayText>
      </DateSection>
      <StatsSection>
        <StatsGroup>
          <StatsRow>
            <StatsTitle>予約</StatsTitle>
            <StatsValue>{formatText("全99件")}</StatsValue>
          </StatsRow>
          <StatsRow>
            <StatsLabel>対面</StatsLabel>
            <StatsValue>{formatText("99件")}</StatsValue>
          </StatsRow>
          <StatsRow>
            <StatsLabel>オンライン</StatsLabel>
            <StatsValue>{formatText("99件")}</StatsValue>
          </StatsRow>
        </StatsGroup>
        <StatsGroup>
          <StatsRow>
            <StatsTitle>未完了タスク</StatsTitle>
            <StatsValue>{formatText("99件")}</StatsValue>
          </StatsRow>
        </StatsGroup>
      </StatsSection>
    </ReserveSummarySectionContainer>
  );
};
