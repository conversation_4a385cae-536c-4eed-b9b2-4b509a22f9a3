import { useState, useCallback } from "react";

import dayjs from "dayjs";

import type { ViewType } from "../types";
import type { Dayjs } from "dayjs";

export const useScheduleView = () => {
  const [currentView, setCurrentView] = useState<ViewType>("day");
  const [currentDate, setCurrentDate] = useState<Dayjs>(dayjs());

  const navigateDate = useCallback(
    (direction: "prev" | "next") => {
      setCurrentDate((prevDate) => {
        if (currentView === "day") {
          return direction === "prev"
            ? prevDate.subtract(1, "day")
            : prevDate.add(1, "day");
        } else {
          return direction === "prev"
            ? prevDate.subtract(1, "month")
            : prevDate.add(1, "month");
        }
      });
    },
    [currentView],
  );

  return {
    currentView,
    currentDate,
    setCurrentView,
    navigateDate,
    setCurrentDate,
  };
};
