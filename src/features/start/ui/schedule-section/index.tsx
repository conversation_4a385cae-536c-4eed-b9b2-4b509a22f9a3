import React from "react";

import styled from "styled-components";

import { ScheduleHeader, DayView, MonthView } from "./components";
import { useScheduleView } from "./hooks";

const ScheduleSectionContainer = styled.div`
  height: 380px;
  border-radius: 12px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 20px;
`;

export const ScheduleSection = () => {
  const { currentView, currentDate, setCurrentView, navigateDate } =
    useScheduleView();

  return (
    <ScheduleSectionContainer>
      <ScheduleHeader
        currentView={currentView}
        currentDate={currentDate}
        onViewChange={setCurrentView}
        onNavigateDate={navigateDate}
      />
      {currentView === "day" ? (
        <DayView currentDate={currentDate} />
      ) : (
        <MonthView currentDate={currentDate} />
      )}
    </ScheduleSectionContainer>
  );
};
