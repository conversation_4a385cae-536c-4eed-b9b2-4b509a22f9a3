import React from "react";

import styled from "styled-components";

import type { DayViewProps } from "../types";

const DayViewContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0;
`;

const TimeSlotContainer = styled.div`
  display: flex;
  min-height: 60px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
`;

const TimeLabel = styled.div`
  width: 60px;
  padding: 8px 12px;
  font-size: 12px;
  color: #666;
  border-right: 1px solid #f0f0f0;
  display: flex;
  align-items: flex-start;
  background-color: #fafafa;
`;

const EventArea = styled.div`
  flex: 1;
  padding: 4px 8px;
  position: relative;
  min-height: 52px;
`;

const EventBlock = styled.div<{
  $type: "appointment" | "examination" | "break";
}>`
  background-color: ${({ $type }) => {
    switch ($type) {
      case "appointment":
        return "#b8e6f0";
      case "examination":
        return "#b8e6f0";
      case "break":
        return "#e8e8e8";
      default:
        return "#f0f0f0";
    }
  }};
  border-radius: 4px;
  padding: 6px 12px;
  margin: 2px 0;
  font-size: 12px;
  color: #333;
  border-left: 4px solid
    ${({ $type }) => {
      switch ($type) {
        case "appointment":
          return "#4ebbe0";
        case "examination":
          return "#4ebbe0";
        case "break":
          return "#999";
        default:
          return "#ccc";
      }
    }};
  white-space: pre-line;
  line-height: 1.3;
`;

// Sample time slots for demonstration
const timeSlots = [
  "09:00",
  "10:00",
  "11:00",
  "12:00",
  "13:00",
  "14:00",
  "15:00",
  "16:00",
  "17:00",
];

// Sample events for demonstration
const sampleEvents = [
  {
    id: "1",
    title: "搬入",
    startTime: "09:00",
    endTime: "10:00",
    type: "appointment" as const,
  },
  {
    id: "2",
    title: "患者受入\n検査準備",
    startTime: "13:00",
    endTime: "14:00",
    type: "examination" as const,
  },
];

export const DayView: React.FC<DayViewProps> = ({ events = sampleEvents }) => {
  const getEventsForTimeSlot = (timeSlot: string) => {
    return events.filter((event) => event.startTime === timeSlot);
  };

  return (
    <DayViewContainer>
      {timeSlots.map((timeSlot) => {
        const slotEvents = getEventsForTimeSlot(timeSlot);

        return (
          <TimeSlotContainer key={timeSlot}>
            <TimeLabel>{timeSlot}</TimeLabel>
            <EventArea>
              {slotEvents.map((event) => (
                <EventBlock key={event.id} $type={event.type}>
                  {event.title}
                </EventBlock>
              ))}
            </EventArea>
          </TimeSlotContainer>
        );
      })}
    </DayViewContainer>
  );
};
