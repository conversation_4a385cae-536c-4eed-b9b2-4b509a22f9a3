import React from "react";

import styled from "styled-components";

import type { ViewToggleProps, ViewType } from "../types";

const ToggleContainer = styled.div`
  display: flex;
  border: 1px solid #e2e3e5;
  border-radius: 4px;
  overflow: hidden;
`;

const ToggleButton = styled.button<{ $isActive: boolean }>`
  padding: 8px 16px;
  border: none;
  background-color: ${({ $isActive }) => ($isActive ? "#4ebbe0" : "#fff")};
  color: ${({ $isActive }) => ($isActive ? "#fff" : "#333")};
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;

  &:hover {
    background-color: ${({ $isActive }) =>
      $isActive ? "#3da8cc" : "#f8f9fa"};
  }

  &:not(:last-child) {
    border-right: 1px solid #e2e3e5;
  }
`;

export const ViewToggle: React.FC<ViewToggleProps> = ({
  currentView,
  onViewChange,
}) => {
  const toggleOptions: { value: ViewType; label: string }[] = [
    { value: "day", label: "日" },
    { value: "month", label: "月" },
  ];

  return (
    <ToggleContainer>
      {toggleOptions.map((option) => (
        <ToggleButton
          key={option.value}
          $isActive={currentView === option.value}
          onClick={() => onViewChange(option.value)}
        >
          {option.label}
        </ToggleButton>
      ))}
    </ToggleContainer>
  );
};
