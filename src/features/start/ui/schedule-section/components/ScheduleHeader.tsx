import React from "react";

import styled from "styled-components";

import { DateNavigation } from "./DateNavigation";
import { ViewToggle } from "./ViewToggle";

import type { ScheduleHeaderProps } from "../types";

const HeaderContainer = styled.div`
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
`;

const Title = styled.h3`
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const ScheduleHeader: React.FC<ScheduleHeaderProps> = ({
  currentView,
  currentDate,
  onViewChange,
  onNavigateDate,
}) => {
  return (
    <HeaderContainer>
      <Title>スケジュール</Title>
      <RightSection>
        <DateNavigation
          currentDate={currentDate}
          currentView={currentView}
          onNavigate={onNavigateDate}
        />
        <ViewToggle currentView={currentView} onViewChange={onViewChange} />
      </RightSection>
    </HeaderContainer>
  );
};
