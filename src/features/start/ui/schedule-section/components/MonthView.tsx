import React from "react";

import holidayJp from "@holiday-jp/holiday_jp";
import styled from "styled-components";

import type { Dayjs } from "dayjs";
import type { MonthViewProps } from "../types";

const MonthViewContainer = styled.div`
  flex: 1;
`;

const CalendarGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background-color: #e2e3e5;
  border: 1px solid #e2e3e5;
  overflow: hidden;

  .not-current-month {
    gap: 0 !important;
    border: none !important;
  }
`;

const WeekdayHeader = styled.div`
  height: 20px;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  color: #243544;
  background-color: #fff;
  line-height: 1;
`;

const WeekdayHeaderRow = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #fff;
`;

const DateCell = styled.div<{
  $isCurrentMonth: boolean;
  $isToday: boolean;
  $isWeekend: boolean;
  $isHoliday: boolean;
}>`
  background-color: #fff;
  height: 54px;
  padding: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  ${({ $isCurrentMonth }) =>
    !$isCurrentMonth &&
    `
    background-color: #f5f5f5;
    color: #ccc;
    border: none;
    pointer-events: none;
    gap: 0;
  `}

  ${({ $isToday }) =>
    $isToday &&
    `
    background-color: #e3f2fd;
    font-weight: 600;
  `}

  ${({ $isWeekend, $isCurrentMonth }) =>
    $isWeekend &&
    $isCurrentMonth &&
    `
    color: #e74c3c;
  `}

  ${({ $isHoliday, $isCurrentMonth }) =>
    $isHoliday &&
    $isCurrentMonth &&
    `
    background-color: #f5f5f5;
    color: #999;
  `}
`;

const DateNumber = styled.span`
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
`;

const EventIndicator = styled.div<{ $type: string }>`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: ${({ $type }) => {
    switch ($type) {
      case "appointment":
        return "#4ebbe0";
      case "examination":
        return "#52c41a";
      case "break":
        return "#999";
      default:
        return "#ccc";
    }
  }};
  margin: 1px;
`;

const EventIndicators = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  justify-content: center;
`;

const weekdays = ["月", "火", "水", "木", "金", "土", "日"];

// Sample events for demonstration
const sampleMonthEvents = [
  { date: 5, type: "appointment" },
  { date: 10, type: "examination" },
  { date: 12, type: "appointment" },
  { date: 13, type: "examination" },
];

export const MonthView: React.FC<MonthViewProps> = ({ currentDate }) => {
  const startOfMonth = currentDate.startOf("month");
  const endOfMonth = currentDate.endOf("month");
  const startOfCalendar = startOfMonth.startOf("week").add(1, "day"); // Start from Monday
  const endOfCalendar = endOfMonth.endOf("week").add(1, "day");

  const calendarDays = [];
  let day = startOfCalendar;

  while (day.isBefore(endOfCalendar)) {
    calendarDays.push(day);
    day = day.add(1, "day");
  }

  const getEventsForDate = (date: number) => {
    return sampleMonthEvents.filter((event) => event.date === date);
  };

  const isToday = (day: Dayjs) => {
    return day.isSame(new Date(), "day");
  };

  const isCurrentMonth = (day: Dayjs) => {
    return day.isSame(currentDate, "month");
  };

  const isWeekend = (day: Dayjs) => {
    return day.day() === 0 || day.day() === 6; // Sunday or Saturday
  };

  const isHoliday = (day: Dayjs) => {
    return holidayJp.isHoliday(day.toDate());
  };

  return (
    <MonthViewContainer>
      <WeekdayHeaderRow>
        {weekdays.map((weekday) => (
          <WeekdayHeader key={weekday}>{weekday}</WeekdayHeader>
        ))}
      </WeekdayHeaderRow>
      <CalendarGrid>
        {calendarDays.map((day, index) => {
          const dayEvents = getEventsForDate(day.date());
          const isCurrent = isCurrentMonth(day);

          return (
            <DateCell
              key={index}
              $isCurrentMonth={isCurrent}
              $isToday={isToday(day)}
              $isWeekend={isWeekend(day)}
              $isHoliday={isHoliday(day)}
              className={isCurrent ? undefined : "not-current-month"}
            >
              {isCurrent && (
                <>
                  <DateNumber>{day.date()}</DateNumber>
                  <EventIndicators>
                    {dayEvents.map((event, eventIndex) => (
                      <EventIndicator key={eventIndex} $type={event.type} />
                    ))}
                  </EventIndicators>
                </>
              )}
            </DateCell>
          );
        })}
      </CalendarGrid>
    </MonthViewContainer>
  );
};
