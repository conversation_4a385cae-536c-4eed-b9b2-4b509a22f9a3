import React from "react";

import styled from "styled-components";

import type { DateNavigationProps } from "../types";

const NavigationContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const NavigationButton = styled.button`
  width: 32px;
  height: 32px;
  border: 1px solid #e2e3e5;
  border-radius: 4px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
    border-color: #d1d5db;
  }

  &:active {
    background-color: #e5e7eb;
  }
`;

const DateText = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: #333;
  min-width: 120px;
  text-align: center;
`;

const ArrowIcon = styled.span<{ direction: "left" | "right" }>`
  width: 0;
  height: 0;
  border-style: solid;
  ${({ direction }) =>
    direction === "left"
      ? `
        border-width: 4px 6px 4px 0;
        border-color: transparent #666 transparent transparent;
      `
      : `
        border-width: 4px 0 4px 6px;
        border-color: transparent transparent transparent #666;
      `}
`;

export const DateNavigation: React.FC<DateNavigationProps> = ({
  currentDate,
  currentView,
  onNavigate,
}) => {
  const formatDate = () => {
    if (currentView === "day") {
      return currentDate.format("YYYY年M月D日");
    } else {
      return currentDate.format("YYYY年M月");
    }
  };

  return (
    <NavigationContainer>
      <NavigationButton onClick={() => onNavigate("prev")}>
        <ArrowIcon direction="left" />
      </NavigationButton>
      <DateText>{formatDate()}</DateText>
      <NavigationButton onClick={() => onNavigate("next")}>
        <ArrowIcon direction="right" />
      </NavigationButton>
    </NavigationContainer>
  );
};
