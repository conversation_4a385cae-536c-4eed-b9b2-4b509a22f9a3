import type { Dayjs } from "dayjs";

export type ViewType = "day" | "month";

export type ScheduleEvent = {
  id: string;
  title: string;
  startTime: string;
  endTime: string;
  type: "appointment" | "examination" | "break";
};

export type ScheduleHeaderProps = {
  currentView: ViewType;
  currentDate: Dayjs;
  onViewChange: (view: ViewType) => void;
  onNavigateDate: (direction: "prev" | "next") => void;
};

export type DayViewProps = {
  currentDate: Dayjs;
  events?: ScheduleEvent[];
};

export type MonthViewProps = {
  currentDate: Dayjs;
  events?: ScheduleEvent[];
};

export type DateNavigationProps = {
  currentDate: Dayjs;
  currentView: ViewType;
  onNavigate: (direction: "prev" | "next") => void;
};

export type ViewToggleProps = {
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
};
